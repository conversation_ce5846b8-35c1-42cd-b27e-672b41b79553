// File: chatgpt-chatbot.js

function sendMessage() {
    const input = document.getElementById('userInput');
    const message = input.value.trim();
    if (!message) return;

    appendMessage('You', message);
    input.value = '';

    // Get context-enhanced message if the function is available
    const contextMessage = window.getSiteContextMessage ? window.getSiteContextMessage(message) : message;

    fetch(chatgpt_vars.ajax_url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            action: 'chatgpt_send_message',
            message: contextMessage
        })
    })
    .then(res => res.json())
    .then(data => {
        if (data.success && data.data) {
            appendMessage('Bot', data.data);
        } else {
            appendMessage('Bot', 'Sorry, I did not understand that.');
        }
    })
    .catch(() => appendMessage('Bot', 'Error sending request.'));
}

function appendMessage(sender, text) {
    const chatbox = document.getElementById('chatbox');
    const msgDiv = document.createElement('div');
    msgDiv.className = 'chat-message';
    msgDiv.innerHTML = `<strong>${sender}:</strong> ${text}`;
    chatbox.appendChild(msgDiv);
    chatbox.scrollTop = chatbox.scrollHeight;
}
