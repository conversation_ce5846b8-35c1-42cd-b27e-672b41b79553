// File: chatgpt-chatbot.js

function sendMessage() {
    const input = document.getElementById('userInput');
    const message = input.value.trim();
    if (!message) return;

    appendMessage('You', message);
    input.value = '';

    // Get context-enhanced message if the function is available
    const contextMessage = window.getSiteContextMessage ? window.getSiteContextMessage(message) : message;
    console.log('Original message:', message); // Debug log
    console.log('Context message:', contextMessage); // Debug log

    fetch(chatgpt_vars.ajax_url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            action: 'chatgpt_send_message',
            message: contextMessage
        })
    })
    .then(res => res.json())
    .then(data => {
        console.log('API Response:', data); // Debug log
        console.log('data.success:', data.success); // Debug log
        console.log('data.data:', data.data); // Debug log
        console.log('typeof data.data:', typeof data.data); // Debug log

        if (data.success && data.data !== undefined && data.data !== null) {
            // Ensure we have a valid string response
            const botResponse = typeof data.data === 'string' ? data.data : JSON.stringify(data.data);
            appendMessage('Bot', botResponse);
        } else {
            console.error('API Error:', data); // Debug log
            const errorMsg = data.data || data.message || 'Unknown error';
            appendMessage('Bot', 'Sorry, I did not understand that. Error: ' + errorMsg);
        }
    })
    .catch(error => {
        console.error('Request Error:', error); // Debug log
        appendMessage('Bot', 'Error sending request: ' + error.message);
    });
}

function appendMessage(sender, text) {
    const chatbox = document.getElementById('chatbox');
    const msgDiv = document.createElement('div');
    msgDiv.className = 'chat-message';
    msgDiv.innerHTML = `<strong>${sender}:</strong> ${text}`;
    chatbox.appendChild(msgDiv);
    chatbox.scrollTop = chatbox.scrollHeight;
}
