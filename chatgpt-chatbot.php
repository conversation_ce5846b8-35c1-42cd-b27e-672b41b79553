<?php
/**
 * Plugin Name: ChatGPT Chatbot for WordPress
 * Description: A simple chatbot using OpenAI's ChatGPT API.
 * Version: 1.0
 * Author: Your Name
 */

// Enqueue JS and CSS
add_action('wp_enqueue_scripts', 'chatgpt_chatbot_enqueue_scripts');
function chatgpt_chatbot_enqueue_scripts() {
    wp_enqueue_script('chatgpt-site-context', plugin_dir_url(__FILE__) . 'site-context.js', array(), null, true);
    wp_enqueue_script('chatgpt-chatbot', plugin_dir_url(__FILE__) . 'chatgpt-chatbot.js', array('jquery', 'chatgpt-site-context'), null, true);
    wp_localize_script('chatgpt-chatbot', 'chatgpt_vars', array(
        'ajax_url' => admin_url('admin-ajax.php')
    ));
    wp_enqueue_style('chatgpt-chatbot-style', plugin_dir_url(__FILE__) . 'chatgpt-chatbot.css');
}

// Shortcode to display chatbot
add_shortcode('chatgpt_chatbot', 'chatgpt_chatbot_display');
function chatgpt_chatbot_display() {
    return '<div id="chatbot">
        <div id="chatbox"></div>
        <input type="text" id="userInput" placeholder="Ask me anything...">
        <button onclick="sendMessage()">Send</button>
    </div>';
}

// AJAX handler
add_action('wp_ajax_nopriv_chatgpt_send_message', 'chatgpt_send_message');
add_action('wp_ajax_chatgpt_send_message', 'chatgpt_send_message');
function chatgpt_send_message() {
    $message = sanitize_text_field($_POST['message']);
    $api_key = '********************************************************************************************************************************************************************';

    $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
        ),
        'body' => json_encode(array(
            'model' => 'gpt-4',
            'messages' => array(
                array('role' => 'user', 'content' => $message)
            )
        ))
    ));

    if (is_wp_error($response)) {
        wp_send_json_error('Request failed');
    } else {
        $body = json_decode(wp_remote_retrieve_body($response), true);
        wp_send_json_success($body['choices'][0]['message']['content'] ?? 'No response from AI');
    }

    wp_die();
}
