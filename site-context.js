// File: site-context.js
// Provides website context to enhance chatbot responses

window.getSiteContextMessage = function(userMessage) {
    // Gather website information
    const siteInfo = {
        title: document.title || 'Unknown',
        url: window.location.href,
        domain: window.location.hostname,
        path: window.location.pathname,
        description: '',
        keywords: '',
        headings: [],
        links: [],
        images: [],
        forms: []
    };

    // Get meta description
    const metaDesc = document.querySelector('meta[name="description"]');
    if (metaDesc) {
        siteInfo.description = metaDesc.getAttribute('content') || '';
    }

    // Get meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
        siteInfo.keywords = metaKeywords.getAttribute('content') || '';
    }

    // Get main headings (H1, H2, H3)
    const headings = document.querySelectorAll('h1, h2, h3');
    headings.forEach(heading => {
        if (heading.textContent.trim()) {
            siteInfo.headings.push({
                level: heading.tagName.toLowerCase(),
                text: heading.textContent.trim().substring(0, 100) // Limit length
            });
        }
    });

    // Get navigation links
    const navLinks = document.querySelectorAll('nav a, .menu a, .navigation a');
    navLinks.forEach(link => {
        if (link.textContent.trim() && link.href) {
            siteInfo.links.push({
                text: link.textContent.trim().substring(0, 50),
                url: link.href
            });
        }
    });

    // Get images with alt text
    const images = document.querySelectorAll('img[alt]');
    images.forEach(img => {
        if (img.alt.trim()) {
            siteInfo.images.push({
                alt: img.alt.trim().substring(0, 100),
                src: img.src
            });
        }
    });

    // Get form information
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const formInfo = {
            action: form.action || 'current page',
            method: form.method || 'get',
            inputs: []
        };

        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type !== 'hidden' && input.name) {
                formInfo.inputs.push({
                    type: input.type || input.tagName.toLowerCase(),
                    name: input.name,
                    placeholder: input.placeholder || '',
                    label: input.labels && input.labels[0] ? input.labels[0].textContent.trim() : ''
                });
            }
        });

        if (formInfo.inputs.length > 0) {
            siteInfo.forms.push(formInfo);
        }
    });

    // Build context message
    let contextMessage = `User Question: ${userMessage}\n\n`;
    contextMessage += `Website Context:\n`;
    contextMessage += `- Site: ${siteInfo.title} (${siteInfo.domain})\n`;
    contextMessage += `- Current Page: ${siteInfo.path}\n`;
    
    if (siteInfo.description) {
        contextMessage += `- Description: ${siteInfo.description}\n`;
    }
    
    if (siteInfo.keywords) {
        contextMessage += `- Keywords: ${siteInfo.keywords}\n`;
    }

    if (siteInfo.headings.length > 0) {
        contextMessage += `- Page Headings:\n`;
        siteInfo.headings.slice(0, 5).forEach(heading => {
            contextMessage += `  ${heading.level.toUpperCase()}: ${heading.text}\n`;
        });
    }

    if (siteInfo.links.length > 0) {
        contextMessage += `- Navigation Links:\n`;
        siteInfo.links.slice(0, 8).forEach(link => {
            contextMessage += `  ${link.text}\n`;
        });
    }

    if (siteInfo.forms.length > 0) {
        contextMessage += `- Available Forms:\n`;
        siteInfo.forms.slice(0, 3).forEach(form => {
            contextMessage += `  Form (${form.method.toUpperCase()}): `;
            contextMessage += form.inputs.map(input => input.label || input.name).join(', ') + '\n';
        });
    }

    contextMessage += `\nPlease provide a helpful response based on this website context and the user's question.`;

    return contextMessage;
};
